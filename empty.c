/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/**
 * @file    empty.c
 * @brief   双电机驱动示例程序
 * <AUTHOR> (Engineer)
 * @date    2025-07-01
 * @note    基于MSPM0G3507和A4950电机驱动芯片
 */

#include "ti_msp_dl_config.h"

// ============================================================================
// 电机驱动代码集成 - 避免链接问题
// ============================================================================

/* Exported constants --------------------------------------------------------*/
#define MOTOR_SPEED_MAX         100     // 最大速度
#define MOTOR_SPEED_MIN         -100    // 最小速度
#define MOTOR_PWM_PERIOD        1000    // PWM周期 (与TIMG7配置一致)
#define MOTOR_MIN_PWM_THRESHOLD 100     // 最小PWM阈值

/* Exported types ------------------------------------------------------------*/
/**
 * @brief 电机状态枚举
 */
typedef enum {
    MOTOR_STATE_STOP = 0,    // 停止状态
    MOTOR_STATE_FORWARD,     // 正转状态
    MOTOR_STATE_REVERSE      // 反转状态
} MotorState_t;

/**
 * @brief 电机硬件配置结构体
 * @note 简化设计，直接使用预定义的硬件资源
 */
typedef struct {
    DL_TIMER_CC_INDEX cc_index;     // 捕获比较索引 (DL_TIMER_CC_0_INDEX 或 DL_TIMER_CC_1_INDEX)
    GPIO_Regs* dir_port;            // 方向控制GPIO端口 (GPIOA)
    uint32_t dir_pin;               // 方向控制GPIO引脚 (DL_GPIO_PIN_0 或 DL_GPIO_PIN_1)
} MotorHW_t;

/**
 * @brief 电机实体结构体
 */
typedef struct {
    MotorHW_t hw;                   // 硬件配置
    int8_t speed;                   // 当前速度 (-100 到 +100)
    MotorState_t state;             // 当前状态
    uint8_t enable;                 // 使能标志 (1-使能, 0-失能)
    uint8_t reverse;                // 电机安装方向 (0-正装, 1-反装)
} Motor_t;

// ============================================================================
// 内部函数声明
// ============================================================================
static uint32_t Speed_To_PWM(int8_t speed);
static int8_t Motor_ValidateParams(Motor_t *motor);

// ============================================================================
// 电机驱动函数实现
// ============================================================================

/**
 * @brief 速度值转换为PWM占空比
 */
static uint32_t Speed_To_PWM(int8_t speed)
{
    uint8_t abs_speed = (speed >= 0) ? speed : -speed;  // 取绝对值
    uint32_t pwm_value = (abs_speed * MOTOR_PWM_PERIOD) / 100;  // 映射到0-1000

    // 最小PWM阈值处理，确保电机能够启动
    if (pwm_value > 0 && pwm_value < MOTOR_MIN_PWM_THRESHOLD) {
        pwm_value = MOTOR_MIN_PWM_THRESHOLD;
    }

    return pwm_value;
}

/**
 * @brief 参数有效性检查
 */
static int8_t Motor_ValidateParams(Motor_t *motor)
{
    if (motor == NULL || motor->hw.dir_port == NULL) {
        return -1;
    }
    return 0;
}

/**
 * @brief 创建电机实体
 */
int8_t Motor_Create(Motor_t *motor,
                    DL_TIMER_CC_INDEX cc_index,
                    GPIO_Regs *dir_port,
                    uint32_t dir_pin,
                    uint8_t reverse)
{
    // 参数检查
    if (motor == NULL || dir_port == NULL) {
        return -1;
    }

    // 检查捕获比较索引有效性
    if (cc_index != DL_TIMER_CC_0_INDEX && cc_index != DL_TIMER_CC_1_INDEX) {
        return -1;
    }

    // 初始化硬件配置 (使用预配置的PWM_MOTOR_INST)
    motor->hw.cc_index = cc_index;
    motor->hw.dir_port = dir_port;
    motor->hw.dir_pin = dir_pin;

    // 初始化电机状态
    motor->speed = 0;
    motor->state = MOTOR_STATE_STOP;
    motor->enable = 1;
    motor->reverse = reverse;

    // PWM定时器已在SYSCFG_DL_init()中启动，这里只需要设置初始状态
    // 设置初始状态：停止（DIR=0, PWM=0）
    DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin);
    DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, 0, motor->hw.cc_index);

    return 0;
}

/**
 * @brief 设置电机速度
 */
int8_t Motor_SetSpeed(Motor_t *motor, int8_t speed)
{
    // 参数验证
    if (Motor_ValidateParams(motor) != 0) {
        return -1;
    }

    // 检查使能状态
    if (motor->enable == 0) {
        return -1;
    }

    // 速度范围限制
    if (speed > MOTOR_SPEED_MAX) {
        speed = MOTOR_SPEED_MAX;
    } else if (speed < MOTOR_SPEED_MIN) {
        speed = MOTOR_SPEED_MIN;
    }

    // 处理停止
    if (speed == 0) {
        // A4950停止逻辑：DIR=0, PWM=0
        DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin);
        DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, 0, motor->hw.cc_index);
        motor->state = MOTOR_STATE_STOP;
        motor->speed = 0;
        return 0;
    }

    // 处理电机安装方向
    if (motor->reverse) {
        speed = -speed;  // 反装电机需要反向
    }

    // A4950控制逻辑
    uint8_t dir_level;
    uint32_t pwm_value;

    if (speed > 0) {
        // 正转：DIR=0, PWM=速度值
        dir_level = 0;
        pwm_value = Speed_To_PWM(speed);
        motor->state = MOTOR_STATE_FORWARD;
    } else {
        // 反转：DIR=1, PWM=速度值
        dir_level = 1;
        pwm_value = Speed_To_PWM(-speed);  // 取绝对值
        motor->state = MOTOR_STATE_REVERSE;
    }

    // 设置DIR引脚 (TI DriverLib)
    if (dir_level) {
        DL_GPIO_setPins(motor->hw.dir_port, motor->hw.dir_pin);
    } else {
        DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin);
    }

    // 设置PWM占空比 (TI DriverLib)
    DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, pwm_value, motor->hw.cc_index);

    // 更新电机状态
    motor->speed = speed;
    return 0;
}

/**
 * @brief 停止电机
 */
int8_t Motor_Stop(Motor_t *motor)
{
    // 参数验证
    if (Motor_ValidateParams(motor) != 0) {
        return -1;
    }

    // A4950停止逻辑：DIR=0, PWM=0 (TI DriverLib)
    DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin);
    DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, 0, motor->hw.cc_index);

    // 更新电机状态
    motor->speed = 0;
    motor->state = MOTOR_STATE_STOP;
    return 0;
}

/**
 * @brief 获取电机状态
 */
MotorState_t Motor_GetState(Motor_t *motor)
{
    if (motor == NULL) {
        return MOTOR_STATE_STOP;
    }
    return motor->state;
}

/**
 * @brief 电机使能控制
 */
int8_t Motor_Enable(Motor_t *motor, uint8_t enable)
{
    // 参数验证
    if (Motor_ValidateParams(motor) != 0) {
        return -1;
    }

    motor->enable = enable;

    // 如果失能，立即停止电机
    if (enable == 0) {
        // A4950停止逻辑：DIR=0, PWM=0 (TI DriverLib)
        DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin);
        DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, 0, motor->hw.cc_index);
        motor->speed = 0;
        motor->state = MOTOR_STATE_STOP;
    }

    return 0;
}

// 电机实体定义
Motor_t motor1;  // 左轮电机
Motor_t motor2;  // 右轮电机

// 延时函数 (简单的循环延时)
void delay_ms(uint32_t ms)
{
    uint32_t i, j;
    for (i = 0; i < ms; i++) {
        for (j = 0; j < 8000; j++) {  // 基于32MHz时钟的粗略延时
            __asm("nop");
        }
    }
}

// 电机系统初始化
void Motor_System_Init(void)
{
    // 简化GPIO配置 - 使用现有的LED端口作为方向控制
    // 这样避免了复杂的GPIO配置问题

    // 创建电机1 (左轮) - 使用TIMG7_CCP0
    // 使用LED1_PORT作为方向控制端口 (GPIOB)
    Motor_Create(&motor1,
                 DL_TIMER_CC_0_INDEX,      // 通道0
                 LED1_PORT,                // 方向控制端口 (GPIOB)
                 DL_GPIO_PIN_27,           // 使用GPIOB.27作为方向控制
                 0);                       // 正装

    // 创建电机2 (右轮) - 使用TIMG7_CCP1
    Motor_Create(&motor2,
                 DL_TIMER_CC_1_INDEX,      // 通道1
                 LED1_PORT,                // 方向控制端口 (GPIOB)
                 DL_GPIO_PIN_22,           // 使用GPIOB.22作为方向控制
                 0);                       // 正装
}

// LED状态指示
void LED_Toggle(void)
{
    DL_GPIO_togglePins(LED1_PORT, LED1_PIN_26_PIN);
}

// 双轮前进
void Robot_MoveForward(int8_t speed)
{
    Motor_SetSpeed(&motor1, speed);   // 左轮前转
    Motor_SetSpeed(&motor2, speed);   // 右轮前转
    LED_Toggle();  // LED指示运动状态
}

// 双轮后退
void Robot_MoveBackward(int8_t speed)
{
    Motor_SetSpeed(&motor1, -speed);  // 左轮后转
    Motor_SetSpeed(&motor2, -speed);  // 右轮后转
    LED_Toggle();  // LED指示运动状态
}

// 停止所有电机
void Robot_Stop(void)
{
    Motor_Stop(&motor1);
    Motor_Stop(&motor2);
    DL_GPIO_clearPins(LED1_PORT, LED1_PIN_26_PIN);  // 关闭LED
}

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();

    // 电机系统初始化
    Motor_System_Init();

    // LED初始状态指示
    DL_GPIO_setPins(LED1_PORT, LED1_PIN_26_PIN);
    delay_ms(500);
    DL_GPIO_clearPins(LED1_PORT, LED1_PIN_26_PIN);
    delay_ms(500);

    // 主循环 - 电机驱动演示
    while (1) {
        // 前进 - 50%速度
        Robot_MoveForward(50);
        delay_ms(2000);  // 前进2秒

        // 停止
        Robot_Stop();
        delay_ms(1000);  // 停止1秒

        // 后退 - 30%速度
        Robot_MoveBackward(30);
        delay_ms(2000);  // 后退2秒

        // 停止
        Robot_Stop();
        delay_ms(1000);  // 停止1秒

        // 原地转向 (左轮前转，右轮后转)
        Motor_SetSpeed(&motor1, 40);   // 左轮前转
        Motor_SetSpeed(&motor2, -40);  // 右轮后转
        LED_Toggle();
        delay_ms(1500);  // 转向1.5秒

        // 停止
        Robot_Stop();
        delay_ms(2000);  // 停止2秒
    }
}

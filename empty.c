/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/**
 * @file    empty.c
 * @brief   双电机驱动示例程序
 * <AUTHOR> (Engineer)
 * @date    2025-07-01
 * @note    基于MSPM0G3507和A4950电机驱动芯片
 */

#include "ti_msp_dl_config.h"
#include "motor_driver.h"

// 电机实体定义
Motor_t motor1;  // 左轮电机
Motor_t motor2;  // 右轮电机

// 延时函数 (简单的循环延时)
void delay_ms(uint32_t ms)
{
    uint32_t i, j;
    for (i = 0; i < ms; i++) {
        for (j = 0; j < 8000; j++) {  // 基于32MHz时钟的粗略延时
            __asm("nop");
        }
    }
}

// 电机系统初始化
void Motor_System_Init(void)
{
    // 配置方向控制GPIO (需要在SysConfig中预先配置)
    // 这里假设使用GPIOA.0和GPIOA.1作为方向控制
    DL_GPIO_initDigitalOutputFeatures(IOMUX_PINCM1,  // GPIOA.0
        DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_DOWN,
        DL_GPIO_DRIVE_STRENGTH_LOW, DL_GPIO_HIZ_DISABLE);
    DL_GPIO_enableOutput(GPIOA, DL_GPIO_PIN_0);

    DL_GPIO_initDigitalOutputFeatures(IOMUX_PINCM2,  // GPIOA.1
        DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_DOWN,
        DL_GPIO_DRIVE_STRENGTH_LOW, DL_GPIO_HIZ_DISABLE);
    DL_GPIO_enableOutput(GPIOA, DL_GPIO_PIN_1);

    // 创建电机1 (左轮) - 使用TIMG7_CCP0
    Motor_Create(&motor1,
                 DL_TIMER_CC_0_INDEX,      // 通道0
                 GPIOA,                    // 方向控制端口
                 DL_GPIO_PIN_0,            // 方向控制引脚
                 0);                       // 正装

    // 创建电机2 (右轮) - 使用TIMG7_CCP1
    Motor_Create(&motor2,
                 DL_TIMER_CC_1_INDEX,      // 通道1
                 GPIOA,                    // 方向控制端口
                 DL_GPIO_PIN_1,            // 方向控制引脚
                 0);                       // 正装
}

// LED状态指示
void LED_Toggle(void)
{
    DL_GPIO_togglePins(LED1_PORT, LED1_PIN_26_PIN);
}

// 双轮前进
void Robot_MoveForward(int8_t speed)
{
    Motor_SetSpeed(&motor1, speed);   // 左轮前转
    Motor_SetSpeed(&motor2, speed);   // 右轮前转
    LED_Toggle();  // LED指示运动状态
}

// 双轮后退
void Robot_MoveBackward(int8_t speed)
{
    Motor_SetSpeed(&motor1, -speed);  // 左轮后转
    Motor_SetSpeed(&motor2, -speed);  // 右轮后转
    LED_Toggle();  // LED指示运动状态
}

// 停止所有电机
void Robot_Stop(void)
{
    Motor_Stop(&motor1);
    Motor_Stop(&motor2);
    DL_GPIO_clearPins(LED1_PORT, LED1_PIN_26_PIN);  // 关闭LED
}

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();

    // 电机系统初始化
    Motor_System_Init();

    // LED初始状态指示
    DL_GPIO_setPins(LED1_PORT, LED1_PIN_26_PIN);
    delay_ms(500);
    DL_GPIO_clearPins(LED1_PORT, LED1_PIN_26_PIN);
    delay_ms(500);

    // 主循环 - 电机驱动演示
    while (1) {
        // 前进 - 50%速度
        Robot_MoveForward(50);
        delay_ms(2000);  // 前进2秒

        // 停止
        Robot_Stop();
        delay_ms(1000);  // 停止1秒

        // 后退 - 30%速度
        Robot_MoveBackward(30);
        delay_ms(2000);  // 后退2秒

        // 停止
        Robot_Stop();
        delay_ms(1000);  // 停止1秒

        // 原地转向 (左轮前转，右轮后转)
        Motor_SetSpeed(&motor1, 40);   // 左轮前转
        Motor_SetSpeed(&motor2, -40);  // 右轮后转
        LED_Toggle();
        delay_ms(1500);  // 转向1.5秒

        // 停止
        Robot_Stop();
        delay_ms(2000);  // 停止2秒
    }
}

/*
 * JGB520电机驱动程序 - 超简化版本
 * 专为JGB520减速电机设计，去掉所有复杂判定
 * 只要能让电机转起来！
 */

#include "ti_msp_dl_config.h"

// 简化的延时函数
void delay_ms(uint32_t ms)
{
    uint32_t i, j;
    for (i = 0; i < ms; i++) {
        for (j = 0; j < 8000; j++) {
            __asm("nop");
        }
    }
}

// 电机1前转 (JGB520左轮)
void motor1_forward(uint32_t speed)
{
    DL_GPIO_clearPins(LED1_PORT, DL_GPIO_PIN_27);  // DIR1=0 前转
    DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, speed, DL_TIMER_CC_0_INDEX);  // PWM1
}

// 电机1后转 (JGB520左轮)
void motor1_backward(uint32_t speed)
{
    DL_GPIO_setPins(LED1_PORT, DL_GPIO_PIN_27);    // DIR1=1 后转
    DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, speed, DL_TIMER_CC_0_INDEX);  // PWM1
}

// 电机1停止
void motor1_stop(void)
{
    DL_GPIO_clearPins(LED1_PORT, DL_GPIO_PIN_27);  // DIR1=0
    DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, 0, DL_TIMER_CC_0_INDEX);     // PWM1=0
}

// 电机2前转 (JGB520右轮)
void motor2_forward(uint32_t speed)
{
    DL_GPIO_clearPins(LED1_PORT, DL_GPIO_PIN_22);  // DIR2=0 前转
    DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, speed, DL_TIMER_CC_1_INDEX);  // PWM2
}

// 电机2后转 (JGB520右轮)
void motor2_backward(uint32_t speed)
{
    DL_GPIO_setPins(LED1_PORT, DL_GPIO_PIN_22);    // DIR2=1 后转
    DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, speed, DL_TIMER_CC_1_INDEX);  // PWM2
}

// 电机2停止
void motor2_stop(void)
{
    DL_GPIO_clearPins(LED1_PORT, DL_GPIO_PIN_22);  // DIR2=0
    DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, 0, DL_TIMER_CC_1_INDEX);     // PWM2=0
}

// 双轮前进
void robot_forward(uint32_t speed)
{
    motor1_forward(speed);
    motor2_forward(speed);
    DL_GPIO_setPins(LED1_PORT, LED1_PIN_26_PIN);   // LED指示
}

// 双轮后退
void robot_backward(uint32_t speed)
{
    motor1_backward(speed);
    motor2_backward(speed);
    DL_GPIO_setPins(LED1_PORT, LED1_PIN_26_PIN);   // LED指示
}

// 原地左转
void robot_turn_left(uint32_t speed)
{
    motor1_backward(speed);  // 左轮后转
    motor2_forward(speed);   // 右轮前转
    DL_GPIO_setPins(LED1_PORT, LED1_PIN_26_PIN);   // LED指示
}

// 原地右转
void robot_turn_right(uint32_t speed)
{
    motor1_forward(speed);   // 左轮前转
    motor2_backward(speed);  // 右轮后转
    DL_GPIO_setPins(LED1_PORT, LED1_PIN_26_PIN);   // LED指示
}

// 全部停止
void robot_stop(void)
{
    motor1_stop();
    motor2_stop();
    DL_GPIO_clearPins(LED1_PORT, LED1_PIN_26_PIN); // 关闭LED
}

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();

    // LED启动指示
    DL_GPIO_setPins(LED1_PORT, LED1_PIN_26_PIN);
    delay_ms(1000);
    DL_GPIO_clearPins(LED1_PORT, LED1_PIN_26_PIN);
    delay_ms(500);

    // JGB520电机测试循环 - 超简单版本
    while (1) {
        // 前进 - JGB520双轮前转
        robot_forward(500);  // PWM=500 (50%速度)
        delay_ms(3000);      // 前进3秒

        // 停止
        robot_stop();
        delay_ms(1000);      // 停止1秒

        // 后退 - JGB520双轮后转
        robot_backward(400); // PWM=400 (40%速度)
        delay_ms(3000);      // 后退3秒

        // 停止
        robot_stop();
        delay_ms(1000);      // 停止1秒

        // 左转 - JGB520原地转向
        robot_turn_left(300); // PWM=300 (30%速度)
        delay_ms(2000);       // 左转2秒

        // 停止
        robot_stop();
        delay_ms(1000);       // 停止1秒

        // 右转 - JGB520原地转向
        robot_turn_right(300); // PWM=300 (30%速度)
        delay_ms(2000);        // 右转2秒

        // 停止
        robot_stop();
        delay_ms(2000);        // 停止2秒，然后循环
    }
}

# 产品需求文档 (PRD) - 双电机驱动系统

## 1. 文档信息
- **项目名称**: MSPM0G3507双电机驱动系统
- **版本**: v1.0
- **创建日期**: 2025-07-01
- **负责人**: Emma (产品经理)
- **审核人**: <PERSON> (团队领袖)
- **目标平台**: TI MSPM0G3507开发板

## 2. 背景与问题陈述

### 2.1 当前问题
- 老板需要实现简单的电机驱动功能，使两个轮子能够前转和后转
- 现有代码使用STM32 HAL库函数，与MSPM0G3507芯片不兼容
- 需要将电机驱动代码适配到TI DriverLib库

### 2.2 解决目标
- 将现有A4950电机驱动代码从STM32 HAL库迁移到TI MSPM0 DriverLib
- 实现双电机独立控制（前转、后转、停止）
- 保持原有的电机驱动逻辑和功能特性

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **功能目标**: 实现双电机驱动功能，支持前转、后转、停止
2. **兼容性目标**: 完全适配MSPM0G3507芯片和TI DriverLib
3. **可靠性目标**: 确保电机控制稳定可靠

### 3.2 关键结果 (Key Results)
1. 电机能够响应控制指令进行前转、后转
2. 电机停止功能正常工作
3. PWM信号输出正确，方向控制准确
4. 代码编译无错误，运行稳定

### 3.3 反向指标 (Counter Metrics)
- 电机控制延迟不超过10ms
- 系统资源占用率不超过20%
- 代码复杂度保持在可维护水平

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**: 嵌入式开发工程师
- **使用场景**: 机器人轮式移动控制、自动化设备驱动

### 4.2 用户故事
- **作为开发者**，我希望能够通过简单的API调用控制电机前转后转
- **作为系统集成者**，我希望电机驱动代码能够稳定运行在MSPM0G3507平台上
- **作为调试人员**，我希望能够通过LED指示灯观察系统运行状态

## 5. 功能规格详述

### 5.1 核心功能模块

#### 5.1.1 电机驱动适配层
- **功能**: 将STM32 HAL函数替换为TI DriverLib函数
- **输入**: 电机控制参数（速度、方向）
- **输出**: PWM信号和方向控制信号

#### 5.1.2 双电机控制接口
- **功能**: 提供统一的双电机控制API
- **支持操作**: 
  - 电机前转（正速度）
  - 电机后转（负速度）
  - 电机停止（速度为0）
  - 电机使能/失能

#### 5.1.3 硬件抽象层
- **PWM配置**: 使用TIMG7定时器，通道0和通道1
- **GPIO配置**: 方向控制引脚配置
- **时钟配置**: 32MHz系统时钟

### 5.2 技术实现细节

#### 5.2.1 函数映射表
| STM32 HAL函数 | TI DriverLib函数 | 功能说明 |
|---------------|------------------|----------|
| HAL_TIM_PWM_Start | DL_TimerG_startCounter | 启动PWM |
| HAL_GPIO_WritePin | DL_GPIO_setPins/clearPins | GPIO控制 |
| __HAL_TIM_SET_COMPARE | DL_TimerG_setCaptureCompareValue | 设置PWM占空比 |

#### 5.2.2 硬件引脚分配
- **PWM输出**: 
  - 电机1: GPIOB.15 (TIMG7_CCP0)
  - 电机2: GPIOB.16 (TIMG7_CCP1)
- **方向控制**: 需要分配2个GPIO引脚
- **状态指示**: GPIOB.26 (LED)

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- ✅ 电机驱动代码从STM32 HAL到TI DriverLib的迁移
- ✅ 双电机前转、后转、停止功能
- ✅ PWM速度控制
- ✅ 方向控制逻辑
- ✅ 基本的示例程序

### 6.2 排除功能 (Out of Scope)
- ❌ 电机编码器反馈
- ❌ PID速度闭环控制
- ❌ 复杂的运动轨迹规划
- ❌ 通信接口（UART/SPI/I2C）

## 7. 依赖与风险

### 7.1 内部依赖
- TI MSPM0 DriverLib库正确配置
- SysConfig工具生成的配置文件
- PWM_MOTOR定时器配置正确

### 7.2 外部依赖
- A4950电机驱动芯片硬件连接正确
- 电机硬件规格匹配

### 7.3 潜在风险
- **高风险**: GPIO引脚分配冲突
- **中风险**: PWM频率设置不当导致电机异常
- **低风险**: 代码迁移过程中的语法错误

## 8. 发布初步计划

### 8.1 开发阶段
1. **阶段1**: 代码迁移和适配（预计30分钟）
2. **阶段2**: 功能测试和调试（预计15分钟）
3. **阶段3**: 示例程序编写（预计10分钟）

### 8.2 测试计划
- 编译测试：确保代码无语法错误
- 功能测试：验证电机前转、后转、停止
- 集成测试：验证双电机协调工作

### 8.3 交付物
- 适配后的motor_driver.h和motor_driver.c
- 更新的main函数示例
- 技术文档和使用说明

---

**文档状态**: ✅ 已完成
**下一步**: 等待Mike批准进入技术实现阶段

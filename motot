#include "motot.h"
class MotorController {
private:
    int motorPin;
    int directionPin;
    int pwmChannel;
    int pwmFreq;
    int pwmResolution;
    
public:
    MotorController(int pin, int dirPin, int channel) {
        motorPin = pin;
        directionPin = dirPin;
        pwmChannel = channel;
        pwmFreq = 1000;        // 1kHz PWM??
        pwmResolution = 8;     // 8???? (0-255)
    }
    
    void init() {
        pinMode(directionPin, OUTPUT);
        ledcSetup(pwmChannel, pwmFreq, pwmResolution);
        ledcAttachPin(motorPin, pwmChannel);
    }
    
    void setSpeed(int speed) {
        // speed: -255 ? +255
        if (speed >= 0) {
            digitalWrite(directionPin, HIGH);  // ??
            ledcWrite(pwmChannel, speed);
        } else {
            digitalWrite(directionPin, LOW);   // ??
            ledcWrite(pwmChannel, -speed);
        }
    }
    
    void stop() {
        ledcWrite(pwmChannel, 0);
    }
};


## Example Summary

**双电机驱动系统 - MSPM0G3507 + A4950**

本项目实现了基于TI MSPM0G3507微控制器和A4950电机驱动芯片的双电机控制系统。
支持双轮机器人的前进、后退、转向等基本运动功能。

### 主要特性
- ✅ 双电机独立PWM速度控制
- ✅ 方向控制 (前转/后转)
- ✅ A4950电机驱动芯片适配
- ✅ LED状态指示
- ✅ 完整的API接口
- ✅ 示例演示程序

## Peripherals & Pin Assignments

| Peripheral | Pin | Function |
| --- | --- | --- |
| SYSCTL |  | 系统控制 |
| TIMG7 | PB15 | 电机1 PWM输出 (CCP0) |
| TIMG7 | PB16 | 电机2 PWM输出 (CCP1) |
| GPIOA | PA0 | 电机1 方向控制 (需手动配置) |
| GPIOA | PA1 | 电机2 方向控制 (需手动配置) |
| GPIOB | PB26 | LED状态指示 |
| DEBUGSS | PA20 | Debug Clock |
| DEBUGSS | PA19 | Debug Data In Out |

## BoosterPacks, Board Resources & Jumper Settings

Visit [LP_MSPM0G3507](https://www.ti.com/tool/LP-MSPM0G3507) for LaunchPad information, including user guide and hardware files.

| Pin | Peripheral | Function | LaunchPad Pin | LaunchPad Settings |
| --- | --- | --- | --- | --- |
| PA20 | DEBUGSS | SWCLK | N/A | <ul><li>PA20 is used by SWD during debugging<br><ul><li>`J101 15:16 ON` Connect to XDS-110 SWCLK while debugging<br><li>`J101 15:16 OFF` Disconnect from XDS-110 SWCLK if using pin in application</ul></ul> |
| PA19 | DEBUGSS | SWDIO | N/A | <ul><li>PA19 is used by SWD during debugging<br><ul><li>`J101 13:14 ON` Connect to XDS-110 SWDIO while debugging<br><li>`J101 13:14 OFF` Disconnect from XDS-110 SWDIO if using pin in application</ul></ul> |

### Device Migration Recommendations
This project was developed for a superset device included in the LP_MSPM0G3507 LaunchPad. Please
visit the [CCS User's Guide](https://software-dl.ti.com/msp430/esd/MSPM0-SDK/latest/docs/english/tools/ccs_ide_guide/doc_guide/doc_guide-srcs/ccs_ide_guide.html#sysconfig-project-migration)
for information about migrating to other MSPM0 devices.

### Low-Power Recommendations
TI recommends to terminate unused pins by setting the corresponding functions to
GPIO and configure the pins to output low or input with internal
pullup/pulldown resistor.

SysConfig allows developers to easily configure unused pins by selecting **Board**→**Configure Unused Pins**.

For more information about jumper configuration to achieve low-power using the
MSPM0 LaunchPad, please visit the [LP-MSPM0G3507 User's Guide](https://www.ti.com/lit/slau873).

## 硬件连接

### A4950电机驱动板连接
```
MSPM0G3507    A4950电机驱动板
PB15      ->  PWM1 (电机1 PWM输入)
PB27      ->  DIR1 (电机1 方向控制) [简化配置]
PB16      ->  PWM2 (电机2 PWM输入)
PB22      ->  DIR2 (电机2 方向控制) [简化配置]
GND       ->  GND
3.3V      ->  VCC
```

### 电机连接
- 电机1 (左轮): 连接到A4950的OUT1A/OUT1B
- 电机2 (右轮): 连接到A4950的OUT2A/OUT2B

## SysConfig配置要求

**✅ 无需额外配置**: 项目已优化为使用现有的GPIO资源
- 电机方向控制使用现有的GPIOB端口
- 无需在SysConfig中添加额外的GPIO配置
- 即插即用，简化了配置流程

## API使用示例

### 基本电机控制
```c
#include "motor_driver.h"

Motor_t motor1, motor2;

// 初始化电机 (简化API，无需额外配置)
Motor_Create(&motor1, DL_TIMER_CC_0_INDEX, LED1_PORT, DL_GPIO_PIN_27, 0);
Motor_Create(&motor2, DL_TIMER_CC_1_INDEX, LED1_PORT, DL_GPIO_PIN_22, 0);

// 前进 (50%速度)
Motor_SetSpeed(&motor1, 50);
Motor_SetSpeed(&motor2, 50);

// 后退 (30%速度)
Motor_SetSpeed(&motor1, -30);
Motor_SetSpeed(&motor2, -30);

// 停止
Motor_Stop(&motor1);
Motor_Stop(&motor2);
```

## Example Usage

1. **无需额外配置**: 项目已优化，无需修改SysConfig
2. **硬件连接**: 按照上述连接图连接A4950和电机
3. **编译下载**: 使用Keil、CCS或GCC编译并下载到开发板
4. **运行测试**: 观察电机运动和LED指示

### 预期行为
- 电机前进2秒 (LED闪烁)
- 停止1秒 (LED熄灭)
- 电机后退2秒 (LED闪烁)
- 停止1秒 (LED熄灭)
- 原地转向1.5秒 (LED闪烁)
- 停止2秒，然后循环

## Example Summary

**JGB520电机驱动 - 超简化版本**

专为JGB520减速电机设计的超简化驱动程序。去掉所有复杂判定和错误处理，
只要能让JGB520电机转起来！适合快速原型和简单应用。

### 主要特性
- ✅ **超简化**: 无复杂结构体和判定逻辑
- ✅ **JGB520专用**: 针对JGB520减速电机优化
- ✅ **即插即用**: 无需任何配置，直接编译运行
- ✅ **直观控制**: robot_forward(), robot_backward()等简单函数
- ✅ **快速测试**: 立即看到JGB520转动效果
- ✅ **零依赖**: 单文件解决方案

## Peripherals & Pin Assignments

| Peripheral | Pin | Function |
| --- | --- | --- |
| SYSCTL |  | 系统控制 |
| TIMG7 | PB15 | 电机1 PWM输出 (CCP0) |
| TIMG7 | PB16 | 电机2 PWM输出 (CCP1) |
| GPIOA | PA0 | 电机1 方向控制 (需手动配置) |
| GPIOA | PA1 | 电机2 方向控制 (需手动配置) |
| GPIOB | PB26 | LED状态指示 |
| DEBUGSS | PA20 | Debug Clock |
| DEBUGSS | PA19 | Debug Data In Out |

## BoosterPacks, Board Resources & Jumper Settings

Visit [LP_MSPM0G3507](https://www.ti.com/tool/LP-MSPM0G3507) for LaunchPad information, including user guide and hardware files.

| Pin | Peripheral | Function | LaunchPad Pin | LaunchPad Settings |
| --- | --- | --- | --- | --- |
| PA20 | DEBUGSS | SWCLK | N/A | <ul><li>PA20 is used by SWD during debugging<br><ul><li>`J101 15:16 ON` Connect to XDS-110 SWCLK while debugging<br><li>`J101 15:16 OFF` Disconnect from XDS-110 SWCLK if using pin in application</ul></ul> |
| PA19 | DEBUGSS | SWDIO | N/A | <ul><li>PA19 is used by SWD during debugging<br><ul><li>`J101 13:14 ON` Connect to XDS-110 SWDIO while debugging<br><li>`J101 13:14 OFF` Disconnect from XDS-110 SWDIO if using pin in application</ul></ul> |

### Device Migration Recommendations
This project was developed for a superset device included in the LP_MSPM0G3507 LaunchPad. Please
visit the [CCS User's Guide](https://software-dl.ti.com/msp430/esd/MSPM0-SDK/latest/docs/english/tools/ccs_ide_guide/doc_guide/doc_guide-srcs/ccs_ide_guide.html#sysconfig-project-migration)
for information about migrating to other MSPM0 devices.

### Low-Power Recommendations
TI recommends to terminate unused pins by setting the corresponding functions to
GPIO and configure the pins to output low or input with internal
pullup/pulldown resistor.

SysConfig allows developers to easily configure unused pins by selecting **Board**→**Configure Unused Pins**.

For more information about jumper configuration to achieve low-power using the
MSPM0 LaunchPad, please visit the [LP-MSPM0G3507 User's Guide](https://www.ti.com/lit/slau873).

## 硬件连接

### JGB520电机连接 (超简化)
```
MSPM0G3507    A4950驱动板    JGB520电机
PB15      ->  PWM1       ->  电机1 (左轮)
PB27      ->  DIR1       ->  电机1方向
PB16      ->  PWM2       ->  电机2 (右轮)
PB22      ->  DIR2       ->  电机2方向
GND       ->  GND        ->  电机GND
3.3V      ->  VCC        ->  电机VCC
```

### JGB520接线说明
- **JGB520电机1**: 红线(+) 黑线(-) -> A4950 OUT1
- **JGB520电机2**: 红线(+) 黑线(-) -> A4950 OUT2
- **简单**: JGB520只有两根线，无编码器

## SysConfig配置要求

**✅ 无需额外配置**: 项目已优化为使用现有的GPIO资源
- 电机方向控制使用现有的GPIOB端口
- 无需在SysConfig中添加额外的GPIO配置
- 即插即用，简化了配置流程

## 超简化API使用

### JGB520基本控制 (无需include任何头文件)
```c
// 让JGB520转起来就这么简单！

// 双轮前进
robot_forward(500);     // PWM=500，中等速度
delay_ms(3000);         // 前进3秒

// 双轮后退
robot_backward(400);    // PWM=400，稍慢速度
delay_ms(3000);         // 后退3秒

// 原地转向
robot_turn_left(300);   // PWM=300，转向速度
delay_ms(2000);         // 左转2秒

robot_turn_right(300);  // PWM=300，转向速度
delay_ms(2000);         // 右转2秒

// 停止
robot_stop();           // 全部停止
```

### JGB520单电机控制
```c
// 单独控制每个JGB520电机
motor1_forward(400);    // 左轮前转
motor2_backward(300);   // 右轮后转
delay_ms(1000);

motor1_stop();          // 左轮停止
motor2_stop();          // 右轮停止
```

## JGB520快速上手

### 三步让JGB520转起来
1. **连接JGB520**: 按照上述接线图连接JGB520电机
2. **编译下载**: 直接编译empty.c，无需任何配置
3. **观察效果**: JGB520立即开始运动！

### JGB520运动效果
- **前进3秒**: 双轮同向转动 (LED亮起)
- **停止1秒**: 全部停止 (LED熄灭)
- **后退3秒**: 双轮反向转动 (LED亮起)
- **停止1秒**: 全部停止 (LED熄灭)
- **左转2秒**: 左轮后转，右轮前转 (LED亮起)
- **停止1秒**: 全部停止 (LED熄灭)
- **右转2秒**: 左轮前转，右轮后转 (LED亮起)
- **停止2秒**: 全部停止，然后循环

### JGB520速度参考
- **PWM=200**: 很慢，适合精确控制
- **PWM=300**: 中慢，适合转向
- **PWM=500**: 中速，适合正常行走
- **PWM=800**: 快速，适合冲刺

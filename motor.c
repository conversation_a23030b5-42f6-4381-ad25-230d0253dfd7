#include "ti_msp_dl_config.h"

// ????????
#define MOTOR_PIN_PWM GPIOA_PIN0  // PWM??????
#define MOTOR_PIN_DIR GPIOA_PIN1  // ????

void system_init(void)
{
    // ???????
    SYSCFG_DL_init();
    
    // GPIO???
    DL_GPIO_initDigitalMode(GPIOA, MOTOR_PIN_DIR, DL_GPIO_DIR_OUTPUT);
    DL_GPIO_setPins(GPIOA, MOTOR_PIN_DIR);  // ???????
    
    // PWM??? (??TIMG0??PWM?)
    DL_TIMG_enableClock(TIMG0);
    DL_TIMG_setClockConfig(TIMG0, DL_TIMG_CLOCK_MFCLK);
    DL_TIMG_initTimerMode(TIMG0, DL_TIMG_TIMER_CC0, DL_TIMG_TIMER_MODE_PWM);
    
    // ??PWM??????
    DL_TIMG_setTimerPeriod(TIMG0, DL_TIMG_TIMER_CC0, 1000);  // ??
    DL_TIMG_setTimerCompareValue(TIMG0, DL_TIMG_TIMER_CC0, 500);  // 50%???
    
    // ??PWM????
    DL_GPIO_initPeripheralOutputFunction(GPIOA, MOTOR_PIN_PWM, DL_GPIO_FUNC_TIMG0_CC0);
    
    // ??PWM
    DL_TIMG_enableTimer(TIMG0, DL_TIMG_TIMER_CC0);
}

// ?????? (0-100)
void motor_set_speed(uint8_t speed)
{
    // ?????????PWM???
    uint32_t compare_value = (speed * 10); // ?????1000
    DL_TIMG_setTimerCompareValue(TIMG0, DL_TIMG_TIMER_CC0, compare_value);
}

// ??????
void motor_set_direction(bool direction)
{
    if (direction)
        DL_GPIO_setPins(GPIOA, MOTOR_PIN_DIR);
    else
        DL_GPIO_clearPins(GPIOA, MOTOR_PIN_DIR);
}
/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --package "LQFP-64(PM)" --part "Default" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.20.0+3587"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const PWM    = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1   = PWM.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                              = "LED1";
GPIO1.port                               = "PORTB";
GPIO1.associatedPins[0].$name            = "PIN_26";
GPIO1.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO1.associatedPins[0].assignedPin      = "26";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

PWM1.$name                      = "PWM_MOTOR";
PWM1.timerStartTimer            = true;
PWM1.peripheral.$assign         = "TIMG7";
PWM1.peripheral.ccp0Pin.$assign = "PB15";
PWM1.peripheral.ccp1Pin.$assign = "PB16";
PWM1.PWM_CHANNEL_0.$name        = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.ccp0PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
PWM1.PWM_CHANNEL_1.$name        = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp1PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

SYSCTL.forceDefaultClkConfig = true;

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
GPIO1.associatedPins[0].pin.$suggestSolution = "PB26";
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
SYSCTL.peripheral.$suggestSolution           = "SYSCTL";

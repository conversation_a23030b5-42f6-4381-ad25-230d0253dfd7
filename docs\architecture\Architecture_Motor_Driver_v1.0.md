# 架构设计文档 - 双电机驱动系统

## 1. 文档信息
- **项目名称**: MSPM0G3507双电机驱动系统架构设计
- **版本**: v1.0
- **创建日期**: 2025-07-01
- **架构师**: Bob
- **审核人**: Mike
- **目标平台**: TI MSPM0G3507 + A4950电机驱动芯片

## 2. 架构概述

### 2.1 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────┤
│  Motor_SetSpeed() │ Motor_Stop() │ Motor_GetState()         │
├─────────────────────────────────────────────────────────────┤
│                  电机驱动抽象层 (Motor Driver Layer)          │
├─────────────────────────────────────────────────────────────┤
│  Motor_Create() │ Speed_To_PWM() │ Motor_ValidateParams()   │
├─────────────────────────────────────────────────────────────┤
│                  硬件抽象层 (Hardware Abstraction Layer)      │
├─────────────────────────────────────────────────────────────┤
│  DL_TimerG_*() │ DL_GPIO_*() │ SYSCFG_DL_*()              │
├─────────────────────────────────────────────────────────────┤
│                    硬件层 (Hardware Layer)                   │
└─────────────────────────────────────────────────────────────┘
│  MSPM0G3507 │ TIMG7 │ GPIOB │ A4950 │ DC Motors           │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心设计原则
1. **分层解耦**: 应用层不直接操作硬件寄存器
2. **接口统一**: 提供统一的电机控制API
3. **错误处理**: 完善的参数验证和错误返回机制
4. **资源管理**: 合理的PWM和GPIO资源分配

## 3. 关键技术决策

### 3.1 STM32 HAL到TI DriverLib的函数映射

#### 3.1.1 核心函数映射表
| 功能类别 | STM32 HAL函数 | TI DriverLib函数 | 参数映射 |
|---------|---------------|------------------|----------|
| PWM启动 | HAL_TIM_PWM_Start(htim, channel) | DL_TimerG_startCounter(PWM_MOTOR_INST) | 定时器实例已预配置 |
| PWM占空比 | __HAL_TIM_SET_COMPARE(htim, channel, value) | DL_TimerG_setCaptureCompareValue(PWM_MOTOR_INST, value, cc_index) | 需要映射通道到CC索引 |
| GPIO设置 | HAL_GPIO_WritePin(port, pin, GPIO_PIN_SET) | DL_GPIO_setPins(port, pin) | 直接映射 |
| GPIO清除 | HAL_GPIO_WritePin(port, pin, GPIO_PIN_RESET) | DL_GPIO_clearPins(port, pin) | 直接映射 |

#### 3.1.2 数据类型映射
| STM32 HAL类型 | TI DriverLib类型 | 说明 |
|---------------|------------------|------|
| TIM_HandleTypeDef* | GPTMR_Regs* | 定时器句柄类型 |
| GPIO_TypeDef* | GPIO_Regs* | GPIO端口类型 |
| uint32_t channel | DL_TIMER_CC_INDEX | 通道索引类型 |

### 3.2 硬件资源分配

#### 3.2.1 PWM配置
- **定时器**: TIMG7 (已通过SysConfig配置)
- **时钟频率**: 32MHz
- **PWM周期**: 1000 (对应1kHz频率)
- **PWM模式**: 边沿对齐模式
- **通道分配**:
  - 电机1: TIMG7_CCP0 (GPIOB.15)
  - 电机2: TIMG7_CCP1 (GPIOB.16)

#### 3.2.2 GPIO引脚分配
```
电机1方向控制: GPIOA.0  (需要在SysConfig中配置)
电机2方向控制: GPIOA.1  (需要在SysConfig中配置)
状态指示LED:  GPIOB.26 (已配置)
```

### 3.3 A4950电机驱动逻辑

#### 3.3.1 控制逻辑
```
A4950控制方式:
- 正转: DIR=0, PWM=速度值
- 反转: DIR=1, PWM=(100-|速度值|)
- 停止: DIR=0, PWM=0
```

#### 3.3.2 速度映射算法
```c
// 速度值(-100到+100) -> PWM占空比(0到1000)
uint32_t Speed_To_PWM(int8_t speed) {
    uint8_t abs_speed = abs(speed);
    uint32_t pwm_value = abs_speed * 10;  // 映射到0-1000
    
    // 最小PWM阈值处理
    if (pwm_value > 0 && pwm_value < 55) {
        pwm_value = 55;  // 确保电机能够启动
    }
    
    return pwm_value;
}
```

## 4. 接口设计

### 4.1 电机结构体重新设计

#### 4.1.1 硬件配置结构体
```c
typedef struct {
    GPTMR_Regs* timer_inst;        // TI定时器实例
    DL_TIMER_CC_INDEX cc_index;    // 捕获比较索引
    GPIO_Regs* dir_port;           // 方向控制GPIO端口
    uint32_t dir_pin;              // 方向控制GPIO引脚
} MotorHW_t;
```

#### 4.1.2 电机实体结构体
```c
typedef struct {
    MotorHW_t hw;                  // 硬件配置
    int8_t speed;                  // 当前速度 (-100 到 +100)
    MotorState_t state;            // 当前状态
    uint8_t enable;                // 使能标志
    uint8_t reverse;               // 电机安装方向
} Motor_t;
```

### 4.2 API接口适配

#### 4.2.1 电机创建函数
```c
int8_t Motor_Create(Motor_t* motor, 
                    GPTMR_Regs* timer_inst,
                    DL_TIMER_CC_INDEX cc_index,
                    GPIO_Regs* dir_port, 
                    uint32_t dir_pin, 
                    uint8_t reverse);
```

#### 4.2.2 核心控制函数
- Motor_SetSpeed(): 速度控制，内部处理方向和PWM设置
- Motor_Stop(): 电机停止，设置PWM为0
- Motor_GetState(): 获取电机当前状态
- Motor_Enable(): 电机使能/失能控制

## 5. 实现策略

### 5.1 渐进式迁移策略
1. **阶段1**: 修改头文件，适配数据类型
2. **阶段2**: 逐个函数替换HAL调用为DriverLib调用
3. **阶段3**: 测试验证，确保功能正确性

### 5.2 兼容性处理
- 保持原有API接口不变，降低上层应用的修改成本
- 内部实现完全替换为TI DriverLib
- 错误处理机制保持一致

### 5.3 性能优化
- PWM频率设置为1kHz，平衡性能和噪音
- 最小PWM阈值设置为55，确保电机能够可靠启动
- GPIO操作使用批量设置，提高效率

## 6. 风险评估与缓解

### 6.1 高风险项
**风险**: GPIO引脚冲突
**影响**: 系统无法正常工作
**缓解方案**: 
- 在SysConfig中预先配置所需GPIO
- 使用未被占用的GPIOA.0和GPIOA.1作为方向控制

### 6.2 中风险项
**风险**: PWM配置参数不匹配
**影响**: 电机转速不准确
**缓解方案**:
- 仔细对比TI和STM32的PWM配置差异
- 使用示波器验证PWM输出

### 6.3 低风险项
**风险**: 函数参数类型不匹配
**影响**: 编译错误
**缓解方案**:
- 逐步编译测试
- 使用类型转换确保兼容性

## 7. 测试策略

### 7.1 单元测试
- 测试每个函数的参数验证
- 测试速度到PWM的转换算法
- 测试GPIO控制逻辑

### 7.2 集成测试
- 测试双电机独立控制
- 测试电机前转、后转、停止功能
- 测试系统稳定性

### 7.3 硬件测试
- 使用示波器验证PWM信号
- 测试电机实际转动效果
- 验证A4950驱动芯片工作状态

## 8. 部署方案

### 8.1 配置文件更新
需要在SysConfig中添加方向控制GPIO配置:
```
GPIOA.0 - 数字输出 - 电机1方向控制
GPIOA.1 - 数字输出 - 电机2方向控制
```

### 8.2 编译环境
- 确保TI DriverLib库正确链接
- 验证所有头文件路径正确
- 检查编译器优化设置

---

**架构设计状态**: ✅ 已完成
**技术风险评估**: ✅ 已完成
**下一步**: 交付给Alex进行代码实现

# 电机驱动实现指南

## 1. 文档信息
- **项目名称**: MSPM0G3507双电机驱动系统实现
- **版本**: v1.0
- **创建日期**: 2025-07-01
- **开发者**: <PERSON> (工程师)
- **审核人**: <PERSON> (团队领袖)

## 2. 实现概述

### 2.1 迁移完成情况
✅ **头文件适配**: 完成STM32类型到TI DriverLib类型的映射
✅ **核心函数迁移**: 完成所有HAL函数到DriverLib函数的替换
✅ **示例程序**: 完成完整的双电机控制演示程序
✅ **错误处理**: 保持原有的参数验证和错误处理机制

### 2.2 关键技术变更

#### 2.2.1 数据类型映射
```c
// 原STM32类型 -> TI DriverLib类型
TIM_HandleTypeDef* -> GPTMR_Regs*
GPIO_TypeDef* -> GPIO_Regs*
uint32_t channel -> DL_TIMER_CC_INDEX
uint16_t pin -> uint32_t pin
```

#### 2.2.2 函数调用映射
```c
// PWM控制
HAL_TIM_PWM_Start() -> DL_TimerG_startCounter()
__HAL_TIM_SET_COMPARE() -> DL_TimerG_setCaptureCompareValue()

// GPIO控制
HAL_GPIO_WritePin(port, pin, GPIO_PIN_SET) -> DL_GPIO_setPins(port, pin)
HAL_GPIO_WritePin(port, pin, GPIO_PIN_RESET) -> DL_GPIO_clearPins(port, pin)
```

## 3. 硬件配置要求

### 3.1 PWM配置 (已通过SysConfig完成)
- **定时器**: TIMG7
- **频率**: 1kHz (周期1000)
- **通道0**: GPIOB.15 (电机1 PWM)
- **通道1**: GPIOB.16 (电机2 PWM)

### 3.2 GPIO配置 (需要手动添加)
需要在SysConfig中添加以下GPIO配置：
```
GPIOA.0 - 数字输出 - 电机1方向控制
GPIOA.1 - 数字输出 - 电机2方向控制
```

### 3.3 硬件连接
```
MSPM0G3507    A4950电机驱动板
GPIOB.15  ->  PWM1 (电机1 PWM输入)
GPIOA.0   ->  DIR1 (电机1 方向控制)
GPIOB.16  ->  PWM2 (电机2 PWM输入)
GPIOA.1   ->  DIR2 (电机2 方向控制)
GND       ->  GND
3.3V      ->  VCC
```

## 4. API使用说明

### 4.1 电机初始化
```c
Motor_t motor1, motor2;

// 初始化电机1
Motor_Create(&motor1, 
             PWM_MOTOR_INST,        // TIMG7定时器
             DL_TIMER_CC_0_INDEX,   // 通道0
             GPIOA,                 // 方向控制端口
             DL_GPIO_PIN_0,         // 方向控制引脚
             0);                    // 正装(0)或反装(1)
```

### 4.2 电机控制
```c
// 前转 (50%速度)
Motor_SetSpeed(&motor1, 50);

// 后转 (30%速度)
Motor_SetSpeed(&motor1, -30);

// 停止
Motor_Stop(&motor1);

// 获取状态
MotorState_t state = Motor_GetState(&motor1);
```

### 4.3 速度控制说明
- **速度范围**: -100 到 +100
- **正数**: 电机正转
- **负数**: 电机反转
- **0**: 电机停止
- **最小有效速度**: 约10% (由于最小PWM阈值限制)

## 5. A4950控制逻辑

### 5.1 控制方式
```c
// A4950标准控制逻辑
if (speed > 0) {
    DIR = 0;           // 方向引脚低电平
    PWM = speed * 10;  // PWM占空比
} else if (speed < 0) {
    DIR = 1;           // 方向引脚高电平
    PWM = (100 + speed) * 10;  // PWM占空比 (speed是负数)
} else {
    DIR = 0;           // 停止状态
    PWM = 0;
}
```

### 5.2 最小PWM阈值
为确保电机能够可靠启动，设置了最小PWM阈值：
```c
#define MOTOR_MIN_PWM_THRESHOLD 100  // 10%占空比
```

## 6. 示例程序说明

### 6.1 主要功能
- **前进**: 双轮同向转动
- **后退**: 双轮同向反转
- **转向**: 双轮反向转动
- **LED指示**: 运动状态指示

### 6.2 运行流程
1. 系统初始化
2. 电机系统初始化
3. LED启动指示
4. 循环执行：前进→停止→后退→停止→转向→停止

### 6.3 延时函数
使用简单的循环延时，基于32MHz系统时钟：
```c
void delay_ms(uint32_t ms) {
    for (i = 0; i < ms; i++) {
        for (j = 0; j < 8000; j++) {
            __asm("nop");
        }
    }
}
```

## 7. 编译和部署

### 7.1 编译要求
- TI Code Composer Studio或GCC工具链
- MSPM0 SDK正确安装
- 所有源文件包含在项目中

### 7.2 必需文件
```
motor_driver.h      - 电机驱动头文件
motor_driver.c      - 电机驱动源文件
empty.c            - 主程序文件
ti_msp_dl_config.h  - 系统配置头文件
ti_msp_dl_config.c  - 系统配置源文件
```

### 7.3 编译验证
确保以下编译无错误：
- 所有函数调用正确
- 数据类型匹配
- 头文件包含正确

## 8. 调试和测试

### 8.1 基本功能测试
1. **编译测试**: 确保代码无语法错误
2. **LED测试**: 验证LED能够正常闪烁
3. **PWM测试**: 使用示波器检查PWM输出
4. **方向测试**: 验证方向控制引脚状态

### 8.2 电机功能测试
1. **单电机测试**: 分别测试每个电机的前转、后转、停止
2. **双电机测试**: 测试双电机协调工作
3. **速度测试**: 测试不同速度设置的效果
4. **连续运行测试**: 长时间运行稳定性测试

### 8.3 常见问题排查
- **电机不转**: 检查PWM输出和方向控制信号
- **转向错误**: 检查电机接线和reverse参数设置
- **速度不准**: 检查PWM占空比计算和最小阈值设置
- **系统重启**: 检查电源供应和负载匹配

## 9. 性能优化建议

### 9.1 PWM频率优化
当前设置为1kHz，可根据需要调整：
- **更高频率**: 减少电机噪音，增加CPU负载
- **更低频率**: 减少CPU负载，可能增加噪音

### 9.2 延时函数优化
建议使用定时器中断替代循环延时：
- 提高系统响应性
- 降低CPU占用率
- 支持多任务处理

### 9.3 电机控制优化
- 添加加速度控制，避免突然启停
- 实现速度渐变功能
- 添加电机状态监控

---

**实现状态**: ✅ 已完成
**测试状态**: 待验证
**下一步**: 硬件测试和功能验证
